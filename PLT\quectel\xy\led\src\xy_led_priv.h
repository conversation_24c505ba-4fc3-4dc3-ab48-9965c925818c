#ifndef XY_LED_PRIV_H
#define XY_LED_PRIV_H

#ifdef __cplusplus
extern "C" {
#endif

/* ========== 包含文件 ========== */
#include "ql_gpio.h"

/* ========== 宏定义 ========== */
/* 日志封装 */
#define LED_LOG_INFO(fmt, ...) RTI_LOG("[LED/INFO] " fmt "\r\n", ##__VA_ARGS__)
#define LED_LOG_ERROR(fmt, ...) RTI_LOG("[LED/ERROR] " fmt "\r\n", ##__VA_ARGS__)

/* LED GPIO 映射 - 根据硬件调整 */
#define LED_GPIO_NET_RED GPIO_1
#define LED_GPIO_NET_GREEN GPIO_2
#define LED_GPIO_WIFI_GREEN GPIO_3

/* 任务参数 */
#define LED_TASK_STACK_SIZE (1024)
#define LED_TASK_PRIORITY (26)
#define LED_TASK_NAME "led_ctrl_task"
#define STATUS_TASK_STACK_SIZE (1024)
#define STATUS_TASK_PRIORITY (24)
#define STATUS_TASK_NAME "led_status_task"

/* 事件队列大小 */
#define LED_EVENT_QUEUE_SIZE (10)

/* 时间转换：ms <-> tick （1 tick = 5ms）*/
#define MS_TO_TICK(ms) (((ms) + 4) / 5)

/* 业务相关时间阈值 */
#define BLINK_1HZ_PERIOD_TICK MS_TO_TICK(1000)  /* 1 s */
#define TASK_SLEEP_POLL_TICK MS_TO_TICK(300)    /* 300 ms */
#define TASK_EXIT_TIMEOUT_TICK MS_TO_TICK(2000) /* 2000 ms */
#define TASK_SLEEP_MIN_TICK (1)                 /* 1 tick */

/* ========== 枚举定义 ========== */
/* LED 行为定义 */
typedef enum
{
    LED_OFF = 0,
    LED_ON,
    LED_BLINK_1HZ,
} led_status_e;

/* 网络状态 */
typedef enum
{
    NET_STATUS_NO_SIM = 0,
    NET_STATUS_NO_REG,
    NET_STATUS_REG_GOOD,
    NET_STATUS_REG_POOR,
} led_net_status_e;

/* Wi-Fi 状态 */
typedef enum
{
    WIFI_STATUS_OFF = 0,
    WIFI_STATUS_ON,
} led_wifi_status_e;

/* LED 事件类型 */
typedef enum
{
    LED_EVENT_NET_STATUS_CHANGED = 0x01,
    LED_EVENT_WIFI_STATUS_CHANGED = 0x02,
} led_event_e;

/* LED索引枚举 */
typedef enum
{
    LED_IDX_NET_RED = 0,
    LED_IDX_NET_GREEN,
    LED_IDX_WIFI_GREEN,
    LED_COUNT
} led_idx_e;

/* ========== 结构体定义 ========== */
/* LED 描述符 - 每个 LED 一个 */
typedef struct
{
    ql_gpio_num_e gpio;      /* 硬件引脚 */
    led_status_e status; /* 期望行为 */
} led_desc_t;

/* LED 事件消息 */
typedef struct
{
    led_event_e event_type;
    union {
        led_net_status_e net_status;
        led_wifi_status_e wifi_status;
    } data;
} led_event_msg_t;

#ifdef __cplusplus
}
#endif

#endif /* XY_LED_PRIV_H */
