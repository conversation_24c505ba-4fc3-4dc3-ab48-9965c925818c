#ifndef XY_KEY_PRIV_H
#define XY_KEY_PRIV_H

#ifdef __cplusplus
extern "C" {
#endif

/* ========== 包含文件 ========== */
#include <stdint.h>
#include "UART.h"
#include "ql_gpio.h"

/* ========== 宏定义 ========== */
/* 日志封装 */
#define KEY_LOG_INFO(fmt, ...) RTI_LOG("[KEY/INFO] " fmt "\r\n", ##__VA_ARGS__)
#define KEY_LOG_ERROR(fmt, ...) RTI_LOG("[KEY/ERROR] " fmt "\r\n", ##__VA_ARGS__)

/* 任务参数 */
#define KEY_SCAN_TASK_STACK_SIZE (1024)
#define KEY_SCAN_TASK_PRIORITY 28
#define KEY_SCAN_TASK_NAME "key_scan_task"
#define KEY_EVENT_TASK_STACK_SIZE (2048)
#define KEY_EVENT_TASK_PRIORITY 30
#define KEY_EVENT_TASK_NAME "key_event_task"
#define KEY_MSG_QUEUE_SIZE 16
#define KEY_MSG_QUEUE_NAME "key_msg_queue"

/* 时间转换：ms <-> tick （1 tick = 5ms）*/
#define MS_TO_TICKS(ms) (((ms) + 4) / 5)

/* 业务相关时间阈值 */
#define SCAN_PERIOD_TICKS MS_TO_TICKS(10)           /* 10ms 轮询 */
#define DEBOUNCE_TICKS MS_TO_TICKS(50)              /* 50ms 防抖 */
#define LONG_PRESS_TICKS MS_TO_TICKS(3000)          /* 3s 长按 */
#define DOUBLE_CLICK_TIMEOUT_TICKS MS_TO_TICKS(300) /* 300ms 双击超时 */
#define EXIT_WAIT_TIMEOUT_TICKS MS_TO_TICKS(2000)   /* 2s 退出等待 */
#define TASK_CLEANUP_POLL_INTERVAL_TICKS (1)        /* 1 tick 任务清理轮询间隔 */

/* ========== 枚举定义 ========== */
/* 按键类型 */
typedef enum
{
    KEY_TYPE_FACTORY_RESET = 0,
    KEY_TYPE_MAX
} key_type_e;

/* 事件类型 */
typedef enum
{
    KEY_EVENT_SHORT_PRESS = 0,
    KEY_EVENT_LONG_PRESS,
    KEY_EVENT_DOUBLE_CLICK,
    KEY_EVENT_MAX
} key_event_type_e;

/* 按键状态机状态 */
typedef enum
{
    KEY_STATE_IDLE = 0,
    KEY_STATE_PRESSED,
    KEY_STATE_WAIT_MULTI_CLICK
} key_state_e;

/* 消息类型 */
typedef enum
{
    KEY_MSG_EVENT = 0,
    KEY_MSG_EXIT
} key_msg_type_e;

/* ========== 函数类型定义 ========== */
/* 按键事件回调函数类型 */
typedef void (*key_event_callback_t)(key_event_type_e event_type);

/* ========== 结构体定义 ========== */
/* 按键配置 */
typedef struct
{
    ql_gpio_num_e gpio_pin;
    const char *name;
} key_config_t;

/* 状态上下文 */
typedef struct
{
    key_state_e state;
    uint32_t press_tick;
    uint32_t release_tick;
    uint8_t click_count;
    ql_gpio_level_e last_level;
    key_event_callback_t cb;
} key_ctx_t;

/* 消息队列结构 */
typedef struct
{
    key_msg_type_e msg_type;
    key_type_e key_type;
    key_event_type_e event_type;
} key_msg_t;

#ifdef __cplusplus
}
#endif

#endif /* XY_KEY_PRIV_H */