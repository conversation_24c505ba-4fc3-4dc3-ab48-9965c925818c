-DACIPC_ENABLE_NEW_CALLBACK_MECHANISM -DAICWIFI_100M -DAICWIFI_SUPPORT -DAT_OVER_AP_UART -DBIP_FUNC_SUPPORT -DCAPT_PARAMS_OPTIMIZE -DCA_LONG_IPC_MSG -DCMUX_ENABLE -DCONFIG_KESTREL_A0 -DCONFIG_SDIO_ADMA -DCRANE_WEBUI_SUPPORT -DDDR_STRESS -DDEBUG_D2_MOR_REG_RESEREVED_ENABLE -DDIP_CHN -DDISABLE_NVRAM_ACCESS -DDLM_TAVOR -DEGPRS_MULTISLOT_CLASS=12 -DENABLE_ACIPC -DENABLE_ACIPC_BM_DATA_PATH -DENABLE_EXTEND_MY_AT -DENABLE_MAC_TX_DATA_LOGGING -DENABLE_OOS_HANDLING -DENV_XSCALE -DETHERNET_SUPPORT -DEXT_AT_MODEM_SUPPORT -DFAT32_FILE_SYSTEM -DFEATURE_SHMEM -DFEATURE_WB_AMR_PS -DFG_PLMS_URR -DFLAVOR_COM -DFLAVOR_DDR12MB_GB1MB5 -DFLAVOR_DIET_RAM -DFLAVOR_DUALCORE -DFRBD_AGC_CALIB -DFRBD_CALIB_NVM -DFRBD_DSDS_L1 -DFRBD_FDT_CALIB -DFULL_SYSTEM -DGLOBAL_PS_MODE_LTE_ENABLED -DGLOBAL_PS_MODE_NR_ENABLED -DGPLC_LTE_RSSI_SCAN -DGPRS_MULTISLOT_CLASS=12 -DHL_LWG -DHSPA_MPR -DICS_MBCCH -DICS_MBCCH_2G_RSSI -DINTEL_2CHIP_PLAT -DINTEL_HERMON_SAC -DINTEL_UPGRADE_2SAMPLES_PER_SYMBOL -DINTEL_UPGRADE_DUAL_RAT -DINTEL_UPGRADE_EE_HANDLER_SUPPORT -DINTEL_UPGRADE_EGPRS_M -DINTEL_UPGRADE_GPRS_CIPHER_FLUSH -DINTEL_UPGRADE_GSM_CRL_IF -DINTEL_UPGRADE_R99 -DINTEL_UPGRADE_RF_PARAMS_IN_CF_TDS -DINTEL_UPGRADE_UNIFIED_VOICE_TASK -DI_2CHIP_PLAT -DL1V_NEW_RSSI -DL1_DCXO_ENABLED -DL1_DDR_HIGH_FREQ -DL1_DUAL_MODE -DL1_RX_DIV_SUPPORT -DL1_SW_UPDATE_FOR_DIGRF -DL1_WIFI_LOCATION -DLAPWING -DLAPWING_MCU_DONGLE -DLAPWING_RTOS -DLFS_FILE_SYS -DLTE_GSMMULTIBCCH -DLTE_ONLY_MODE -DLTE_W_PS -DLWIP_IPNETBUF_SUPPORT -DMACRO_FOR_LWG -DMAP_NSS -DMARVELL_UPGRADE_BSIC_REDESIGN -DMIPS_TEST -DMIPS_TEST_RAM -DMRAT_NAS -DMRD_SUPPROT_LZOP -DMSL_INCLUDE -DMSL_POOL_MEM -DMULTI_BCCH_READY_IND -DNEZHA3 -DNEZHA3_1826 -DNO_APLP=0 -DNR_FPGA_BOARD -DNTP -DNVM_INCLUDE -DOSA_NUCLEUS -DOSA_QUEUE_NAMES -DOSA_USED -DPCAC_AT_CLIENT -DPCAC_INCLUDE -DPHS_L1_SW_UPDATE_R7 -DPHS_SW_DEMO -DPHS_SW_DEMO_TTC -DPHS_SW_DEMO_TTC_PM -DPLAT_AQUILAC -DPLAT_LAPWING -DPLAT_TEST -DPLAT_USE_THREADX -DPMICONKEY_ENABLE -DPM_D2FULL_MODE -DPM_D2NONE_MODE -DPM_DEBUG_MODE_ENABLED -DPM_EXT_DBG_INT_ARR -DPPP_ENABLE -DPROTECT_TX_ADMA -DPSM_ENABLE -DQUECTEL_PROJECT_CUST -DRAM_DISK_FILE_SYSTEM -DRELIABLE_DATA -DREMOVE_GSM -DREMOVE_WB -DRF_TYPE_LAPWING -DRUN_WIRELESS_MODEM -DSILICON_PV2 -DSILICON_SEAGULL -DSILICON_TTC_CORE_SEAGULL -DSINGLE_SIM -DSOC_TYPE_LAPWING -DSUPPORT_CM_DUSTER_DIALER -DSUPPORT_PMIC_RTC -DSUPPORT_WIFI_DSP_RESET -DTAVOR -DTAVOR_D2_WB_L1_SUPPORT -DTBD -DTDL1C_SPY_ENABLE -DTFT_QUERY_IND -DTR069_SUPPORT -DTSEN_SUPPORT -DUART_NEW_VERSION -DUPGRADE_ARBEL_PLATFORM -DUPGRADE_DIGRF3G_SUPPORT -DUPGRADE_EDGE -DUPGRADE_EGPRS_M -DUPGRADE_ENHANCED_QUAD_BAND -DUPGRADE_FG_PLMS -DUPGRADE_HERMON_DUAL -DUPGRADE_ICS -DUPGRADE_L1A_FG_PLMS -DUPGRADE_LTE -DUPGRADE_LTE_ONLY -DUPGRADE_MBCCH -DUPGRADE_NR_WIFI -DUPGRADE_PLMS -DUPGRADE_PLMS_3G -DUPGRADE_PLMS_L1 -DUPGRADE_PLMS_SEARCH_API -DUPGRADE_PLMS_SR -DUPGRADE_PLMS_STAGE_2 -DUPGRADE_R4_FS1 -DUPGRDE_TAVOR_COMMUNICATION -DURR_MRAT_ICS_SEARCH -DUSB_CABLE_DETECTION_VIA_PMIC -DUSB_REMOTEWAKEUP -DUSE_TTPCOM_CSR_BLUETOOTH_AUDIO_GAIN_CONTROL -DVOLTE_ENABLE -DWB_FAST_CALIBRATION_USE_LEGACY_TIMER -DWEBUI_SUPPORT -DWHOLE_UMTS_STACK -DWIFI_SUPPORT -DYMODEM_EEH_DUMP -D_DATAOMSL_ENABLED_ -D_DDR_INIT_ -D_DIAG_DISABLE_USB_ -D_DIAG_USE_COMMSTACK_ -D_FDI_USE_OSA_ -D_TAVOR_B0_SILICON_ -D_TAVOR_DIAG_ -D_TAVOR_HARBELL_ -D__TARGET_FEATURE_DOUBLEWORD -IL:\PS\nrps\nr.if -IL:\PLT\env\win32\inc -IL:\PLT\os\threadx\inc -IL:\PLT\hal\usb_standart\inc -IL:\PLT\hal\usb_device\inc -IL:\PLT\hal\usb_device\src\cidriver -IL:\PLT\tavor\Arbel\src -IL:\PLT\tavor\Arbel\inc -IL:\PLT\softutil\nvm\inc -IL:\PLT\3g_ps\rls\tplgsm\security\inc -IL:\PLT\3g_ps\rls\tplgsm\platforms\hermon\modem\l1\3g.mod.w\api\inc -IL:\PLT\hal\usb_cable\inc -IL:\PLT\csw\platform\inc -IL:\PLT\csw\SysCfg\inc -IL:\PLT\softutil\datacollector\inc -IL:\PLT\softutil\datacollector\src -IL:\PLT\hop\HOP\inc -IL:\PLT\hop\BSP\inc -IL:\PLT\hop\BSP\src -IL:\PLT\hop\core\inc -IL:\PLT\hop\core\src -IL:\PLT\hop\intc\inc -IL:\PLT\hop\timer\inc -IL:\PLT\hop\RTC\inc -IL:\PLT\softutil\TickManager\inc -IL:\PLT\hop\dma\inc -IL:\PLT\hop\commpm\inc -IL:\PLT\hop\ccu\inc -IL:\PLT\hop\rm\inc -IL:\PLT\hop\pm\inc -IL:\PLT\hop\pmu\inc -IL:\PLT\hop\wdt\inc -IL:\PLT\hop\UART\inc -IL:\PLT\hop\UART\src -IL:\PLT\pcac\pca_components\inc -IL:\PLT\CrossPlatformSW\CrossPlatformSW\inc -IL:\PLT\hop\ssp\src -IL:\PLT\hop\ssp\inc -IL:\PLT\hal\TIMER\inc -IL:\PLT\CrossPlatformSW\nvmClient\inc -IL:\PLT\CrossPlatformSW\common\inc -IL:\PLT\softutil\csw_memory\inc -IL:\PLT\diag\diag_logic\inc -IL:\PLT\diag\diag_logic\src -IL:\PLT\softutil\EEhandler\inc -IL:\PLT\os\osa\inc -IL:\PLT\os\nu_xscale\inc -IL:\PLT\softutil\FDI\inc -IL:\PLT\softutil\FDI\src\INCLUDE -IL:\PLT\softutil\FDI\src\FDI_ADD -IL:\PLT\softutil\FDI\src\FM_INC -IL:\PLT\hal\core\inc -IL:\PLT\hal\GPIO\inc -IL:\PLT\hal\UART\inc -IL:\PLT\hal\UART\src -IL:\PLT\hal\PMU\inc -IL:\PLT\hal\PMU\src -IL:\PLT\hal\watchdog\inc -IL:\PLT\csw\BSP\inc -IL:\PLT\csw\PM\inc -IL:\PLT\CrossPlatformSW\HSI\inc -IL:\PLT\hal\ACIPC\inc -IL:\PLT\hal\pmic\inc -IL:\PLT\hal\I2C\inc -IL:\PLT\hal\intc\inc -IL:\L1C\drat\plw\inc -IL:\PLT\pcac\msl_dl\inc -IL:\PLT\csw\BSP\src -IL:\PLT\CRD\IPC\inc -IL:\PLT\CRD\wcipher\inc -IL:\PLT\softutil\FDI_7_1\src\common\inc -IL:\PLT\softutil\FDI_7_1\src\platform\mtd\rtos\inc -IL:\PLT\softutil\FDI_7_1\src\platform\oslayer\rtos\nucleus\inc -IL:\PLT\softutil\FDI_7_1\src\ffscore\mfm\common\inc -IL:\PLT\softutil\FDI_7_1\src\platform\oslayer\rtos\api\inc -IL:\PLT\softutil\FDI_7_1\src\platform\flashapi\inc -IL:\PLT\pcac\ci\inc -IL:\PLT\pcac\msl_utils\inc -IL:\PLT\genlib\fsm\inc -IL:\PLT\genlib\qmgr\inc -IL:\PLT\genlib\min_max\inc -IL:\L1C\drat\inc -IL:\L1C\drat\WB\inc -IL:\L1C\drat\DRAT\inc -IL:\L1C\drat\GSM_IF\inc -IL:\L1C\drat\CellularPowerApplication\inc -IL:\PLT\softutil\Monitor\inc -IL:\L1C\aplp\MCL\inc -IL:\L1C\aplp\RF_Driver_SP8\inc -IL:\PLT\hop\telephony\atcmdsrv\inc -IL:\PLT\hop\telephony\atparser\inc -IL:\PLT\softutil\fatsys\fs\hdr -IL:\PLT\pcac\lwipv4v6\src\include\lwip -IL:\PLT\quectel\open\system\inc -IL:\PLT\quectel\open\system\inc\usb\ext_inc -IL:\PLT\quectel\open\common_api\include -IL:\PLT\quectel\open\system\inc\hal -IL:\PLT\quectel\open\system\inc\os -IL:\PLT\quectel\open\system\usb\inc -IL:\PLT\quectel\open\system\usb\ext_inc -IL:\PLT\quectel\open\system\mrd\inc -IL:\PLT\quectel\open\inc\at -IL:\PLT\quectel\open\system\utils\inc -IL:\PLT\quectel\cust\inc -IL:\PLT\pcac\lwipv4v6\src\include -IL:\PLT\pcac\lwipv4v6\src\include\arch -IL:\PLT\pcac\lwipv4v6\src\include\ipv4 -IL:\PLT\pcac\lwipv4v6\src\include\ipv6 -IL:\PLT\pcac\lwipv4v6\src\include\netif -IL:\PLT\hal\PWM\inc -IL:\PLT\hal\MMU\inc -IL:\PLT\hal\usb3_dwc\inc -IL:\PLT\wlanhost\driver\uapapi\inc -IL:\PLT\softutil\aic_wifi\platform\wifi -IL:\PLT\softutil\aic_wifi\host\driver\layer_abstract\inc -IL:\PLT\pcac\duster\inc -IL:\PLT\pcac\dial\inc -IL:\PS\ci\inc -IL:\PLT\os\threadx\src\INT -IL:\PLT\hop\ssipc_mat\inc -IL:\PLT\hop\telephony\modem\inc -IL:\PLT\hal\QSPI_Flash\inc -IL:\PLT\hop\cpmu\inc -IL:\PLT\hal\sulog\inc -IL:\PLT\softutil\flash\inc -IL:\PLT\softutil\EEhandler\src -IL:\PLT\softutil\softutil\inc -IL:\PLT\pcac\tr069\src -IL:\PLT\pcac\mrd\inc -IL:\PLT\ims\ims_client\src\external\mrvxml\inc -IL:\PLT\quectel\xy\led\inc --cpu=Cortex-R5 --fpu=softvfp --littleend --thumb --apcs=/interwork -c -O3 -Otime --no_unaligned_access -g --diag_suppress 2084,1,2,9,61,68,170,175,177,550,1296,2548,2795,4421,6319,9931,9933,3732,2803 --diag_error=warning --gnu --loose_implicit_cast -DDATA_COLLECTOR_IMPL -DISPT_OVER_SSP -DDIAG_SSP_DOUBLE_BUFFER_USE_DYNAMIC_ALLOCATION --feedback=L:/PLT/tavor/Arbel/build/feedbackLinkOpt.txt --diag_suppress=61 --diag_suppress=188