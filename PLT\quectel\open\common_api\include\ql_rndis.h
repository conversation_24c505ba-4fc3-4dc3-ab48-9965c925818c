#ifndef _QL_ATCMD3_H_
#define _QL_ATCMD3_H_

#ifdef __cplusplus
extern "C" {
#endif

/**
 * ����:    rndis ����
 *      
 * @param enable:     1:����rndis, 0:�ر�rn<PERSON>
 * @return            0: �ɹ�,  ����ֵ ʧ��
*/
int ql_rndis_control(int enable);

typedef struct
{
    char gateway[16];  // ����ip������ "************"
    char startip[16];  // dhcp ��ʼip������ "************00"
    char endip[16];    // dhcp ����ip������ "**************" 
}api_dhcp_st; 

/**
 * ����:    dhcp ����  
 *          gateway
 *          startip
 *          endip
 *
 * @return  0 �ɹ�,  < 0 ʧ��
*/
int ql_api_set_dhcpinfo(api_dhcp_st *info);

/**
 * ����:    dhcp ��ѯ
 *
 * @return  0 �ɹ�,  < 0 ʧ��
*/
int ql_api_get_dhcpinfo(api_dhcp_st *info);

/**
 *  ���ܣ�����/�ر�  wifi �� rndis ���ݴ���
 *
 *  @param limitval:  >0: ����, 0: ��<PERSON>wifi rndis ��������, -1: ȡ������
 *                    bytes per period, normal set to Bps
 *  @return  0 �ɹ�,  < 0 ʧ��
*/
int ql_api_lan_speedlimit(int limitval); //�����Ƿ�֧��wlan����

#ifdef __cplusplus
}
#endif

#endif
